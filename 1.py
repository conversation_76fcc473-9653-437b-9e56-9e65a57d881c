import os
import pandas as pd
import numpy as np
from haversine import haversine, Unit
import math


def compute_speed_angle(lat1, lon1, lat2, lon2, t1, t2):
    """计算两点间速度(m/s)和方向角(度)"""
    distance = haversine((lat1, lon1), (lat2, lon2), unit=Unit.METERS)
    time_diff = (pd.to_datetime(t2) - pd.to_datetime(t1)).total_seconds()
    speed = distance / time_diff if time_diff > 0 else 0.0

    d_lon = math.radians(lon2 - lon1)
    y = math.sin(d_lon) * math.cos(math.radians(lat2))
    x = math.cos(math.radians(lat1)) * math.sin(math.radians(lat2)) - \
        math.sin(math.radians(lat1)) * math.cos(math.radians(lat2)) * math.cos(d_lon)
    angle = math.degrees(math.atan2(y, x))
    angle = (angle + 360) % 360

    return speed, angle


def process_trajectory_file(filepath):
    # 跳过前6行头部信息，读取数据
    df = pd.read_csv(filepath, skiprows=6, header=None)
    # Geolife .plt 数据有7列
    df.columns = ['lat', 'lon', 'zero1', 'altitude', 'date_float', 'date', 'time']
    df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])

    speeds = [0.0]
    angles = [0.0]
    for i in range(1, len(df)):
        lat1, lon1 = df.loc[i-1, ['lat', 'lon']]
        lat2, lon2 = df.loc[i, ['lat', 'lon']]
        t1, t2 = df.loc[i-1, 'datetime'], df.loc[i, 'datetime']
        speed, angle = compute_speed_angle(lat1, lon1, lat2, lon2, t1, t2)
        speeds.append(speed)
        angles.append(angle)

    df['speed'] = speeds
    df['angle'] = angles
    return df[['lat', 'lon', 'speed', 'angle']]


def ensure_dir_exists(path):
    os.makedirs(path, exist_ok=True)


def process_multiple_files(original_base, output_base, max_files=10):
    traj_path = os.path.join(original_base, 'Trajectory')
    output_traj_path = os.path.join(output_base, 'Trajectory')
    ensure_dir_exists(output_traj_path)

    files = sorted([f for f in os.listdir(traj_path) if f.endswith('.plt')])[:max_files]

    for f in files:
        input_file = os.path.join(traj_path, f)
        output_file = os.path.join(output_traj_path, f.replace('.plt', '.csv'))
        print(f"处理：{f} → {output_file}")

        df = process_trajectory_file(input_file)
        df.to_csv(output_file, index=False)


if __name__ == '__main__':
    original_folder = 'Geolife Trajectories 1.3/Data/001'
    output_folder = 'Processed/Data/001'
    process_multiple_files(original_folder, output_folder, max_files=3)
    print("✅ 所有文件处理完毕，已保存至新目录。")
