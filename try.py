import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from keras.models import load_model
from keras.losses import MeanSquaredError
from sklearn.preprocessing import MinMaxScaler
from haversine import haversine

# 1. 读取新轨迹文件
data = pd.read_csv('Geolife Trajectories 1.3\\Data\\001\\Trajectory\\20081024234405.plt', skiprows=6, header=None)
data.columns = ['lat', 'lon', 'zero1', 'zero2', 'altitude', 'date', 'time']

print("新轨迹样本数：", len(data))

# 2. 提取坐标
trajectory = data[['lat', 'lon']].values

# 3. 数据归一化
scaler = MinMaxScaler()
trajectory_scaled = scaler.fit_transform(trajectory)

# 4. 构建输入序列
sequence_length = 5
X_new = []
for i in range(len(trajectory_scaled) - sequence_length):
    X_new.append(trajectory_scaled[i:i+sequence_length])
X_new = np.array(X_new)
print("新轨迹 X shape:", X_new.shape)

# 5. 加载模型
model = load_model('geolife_lstm_v3.h5', custom_objects={'mse': MeanSquaredError()})

# 6. 预测轨迹
predictions_scaled = model.predict(X_new)

# 7. 反归一化预测结果
predictions = scaler.inverse_transform(predictions_scaled)

# 8. 误差计算 (Haversine距离)
errors = []
for i in range(len(predictions)):
    pred_point = (predictions[i][0], predictions[i][1])
    true_point = (trajectory[i + sequence_length][0], trajectory[i + sequence_length][1])
    error = haversine(pred_point, true_point)  # 单位 km
    errors.append(error*1000)

avg_error = np.mean(errors)
print(f"平均误差：{avg_error:.4f} m")

# 9. 轨迹可视化
plt.figure(figsize=(10, 6))
plt.plot(trajectory[:, 1], trajectory[:, 0], label='true', color='blue', linewidth=1)
plt.plot(predictions[:, 1], predictions[:, 0], label='predict', color='red', linestyle='--')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.legend()
plt.title('true vs predict')
plt.grid(True)
plt.show()

# 10. 误差分布图
plt.figure(figsize=(10, 4))
plt.plot(errors, label='Pointwise Error (m)')
plt.xlabel('Sample Index')
plt.ylabel('Error (m)')
plt.legend()
plt.title('Prediction Error Distribution')
plt.grid(True)
plt.show()


# 在误差计算之后添加精度计算
threshold = 80  # 100米阈值
correct_predictions = sum(1 for error in errors if error <= threshold)
accuracy = correct_predictions / len(errors) * 100

print(f"平均误差：{avg_error:.4f} m")
print(f"预测精度（100米内）：{accuracy:.2f}%")

# 修改后的完整代码（只显示新增部分，其余部分保持不变）
