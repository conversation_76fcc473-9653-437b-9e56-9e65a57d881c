import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Activation
from keras.optimizers import Adam
from haversine import haversine
import os

plt.rcParams['font.sans-serif'] = ['SimHei']

def load_multiple_trajectories(data_folder, max_files=None):
    """加载多个轨迹文件并合并"""
    all_data = []
    trajectory_folder = os.path.join(data_folder, 'Trajectory')
    
    if not os.path.exists(trajectory_folder):
        print(f"错误：文件夹 {trajectory_folder} 不存在")
        return None
    
    files = sorted([f for f in os.listdir(trajectory_folder) if f.endswith('.csv')])
    if max_files:
        files = files[:max_files]
    
    print(f"找到 {len(files)} 个轨迹文件")
    
    for file in files:
        filepath = os.path.join(trajectory_folder, file)
        try:
            # 读取处理后的CSV文件
            df = pd.read_csv(filepath)
            # 确保列名正确
            if 'lat' in df.columns and 'lon' in df.columns and 'speed' in df.columns and 'angle' in df.columns:
                # 过滤异常值
                df = df[(df['speed'] >= 0) & (df['speed'] <= 50)]  # 速度限制在0-50m/s
                df = df[(df['angle'] >= 0) & (df['angle'] <= 360)]  # 角度限制在0-360度
                
                if len(df) > 10:  # 只保留长度大于10的轨迹
                    all_data.append(df)
                    print(f"加载文件 {file}: {len(df)} 个点")
            else:
                print(f"跳过文件 {file}: 列名不匹配")
        except Exception as e:
            print(f"读取文件 {file} 时出错: {e}")
    
    if all_data:
        combined_data = pd.concat(all_data, ignore_index=True)
        print(f"总共加载 {len(combined_data)} 个轨迹点")
        return combined_data
    else:
        print("没有成功加载任何数据")
        return None

def preprocess_features(data):
    """特征预处理"""
    # 将角度转换为sin和cos分量（避免360度边界问题）
    data['angle_sin'] = np.sin(np.radians(data['angle']))
    data['angle_cos'] = np.cos(np.radians(data['angle']))
    
    # 选择特征：经度、纬度、速度、角度的sin和cos分量
    features = data[['lat', 'lon', 'speed', 'angle_sin', 'angle_cos']].values
    
    return features

def create_dataset_enhanced(dataset, time_step=5):
    """构造增强的数据集"""
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        # 输入序列包含所有特征
        X.append(dataset[i:(i + time_step), :])
        # 输出只预测位置（经纬度）
        Y.append(dataset[i + time_step, :2])  # 只取lat, lon
    return np.array(X), np.array(Y)

def build_enhanced_model(time_step, input_features, output_features=2):
    """构建增强的LSTM模型"""
    model = Sequential()
    
    # 第一层LSTM
    model.add(LSTM(units=128, return_sequences=True, input_shape=(time_step, input_features)))
    model.add(Dropout(0.2))
    
    # 第二层LSTM
    model.add(LSTM(units=128, return_sequences=True))
    model.add(Dropout(0.2))
    
    # 第三层LSTM
    model.add(LSTM(units=64))
    model.add(Dropout(0.2))
    
    # 全连接层
    model.add(Dense(units=32, activation='relu'))
    model.add(Dropout(0.1))
    model.add(Dense(units=output_features))
    model.add(Activation('linear'))
    
    model.compile(loss='mse', optimizer=Adam(learning_rate=0.001), metrics=['mae'])
    return model

def calculate_haversine_error(y_true, y_pred):
    """计算Haversine距离误差"""
    errors = []
    for i in range(len(y_true)):
        true_point = (y_true[i][0], y_true[i][1])
        pred_point = (y_pred[i][0], y_pred[i][1])
        error = haversine(true_point, pred_point) * 1000  # 转换为米
        errors.append(error)
    return np.array(errors)

# 主程序
if __name__ == "__main__":
    # 1. 加载数据
    print("=== 加载轨迹数据 ===")
    data_folder = 'Processed/Data/001'
    data = load_multiple_trajectories(data_folder, max_files=3)
    
    if data is None:
        print("数据加载失败，程序退出")
        exit()
    
    print(f'总样本数：{len(data)}, 原始特征：{data.shape[1]}')
    print("数据统计信息：")
    print(data.describe())
    
    # 2. 特征预处理
    print("\n=== 特征预处理 ===")
    features = preprocess_features(data)
    print(f'预处理后特征维度：{features.shape[1]}')
    
    # 3. 数据归一化
    print("\n=== 数据归一化 ===")
    # 对不同特征使用不同的归一化策略
    scaler_position = MinMaxScaler()  # 位置使用MinMax
    scaler_speed = StandardScaler()   # 速度使用标准化
    scaler_angle = MinMaxScaler()     # 角度分量使用MinMax
    
    # 分别归一化
    features_scaled = np.zeros_like(features)
    features_scaled[:, :2] = scaler_position.fit_transform(features[:, :2])  # lat, lon
    features_scaled[:, 2:3] = scaler_speed.fit_transform(features[:, 2:3])   # speed
    features_scaled[:, 3:5] = scaler_angle.fit_transform(features[:, 3:5])   # angle_sin, angle_cos
    
    # 4. 构造数据集
    print("\n=== 构造训练数据集 ===")
    time_step = 5
    X, Y = create_dataset_enhanced(features_scaled, time_step)
    
    print(f'输入序列形状: {X.shape}')
    print(f'输出标签形状: {Y.shape}')
    
    # 5. 划分训练集和测试集
    train_size = int(len(X) * 0.8)
    trainX, trainY = X[:train_size], Y[:train_size]
    testX, testY = X[train_size:], Y[train_size:]
    
    print(f'训练集 X: {trainX.shape}, Y: {trainY.shape}')
    print(f'测试集 X: {testX.shape}, Y: {testY.shape}')
    
    # 6. 构建和训练模型
    print("\n=== 构建增强LSTM模型 ===")
    model = build_enhanced_model(time_step, X.shape[2], 2)
    model.summary()
    
    print("\n=== 开始训练 ===")
    history = model.fit(
        trainX, trainY, 
        epochs=100, 
        batch_size=64, 
        validation_split=0.2,
        verbose=1
    )
    
    # 7. 模型评估
    print("\n=== 模型评估 ===")
    test_loss, test_mae = model.evaluate(testX, testY, verbose=0)
    print(f'测试集损失: {test_loss:.6f}')
    print(f'测试集MAE: {test_mae:.6f}')
    
    # 8. 预测和反归一化
    print("\n=== 预测结果分析 ===")
    predicted = model.predict(testX, verbose=0)
    
    # 反归一化预测结果和真实值
    predicted_inverse = scaler_position.inverse_transform(predicted)
    testY_inverse = scaler_position.inverse_transform(testY)
    
    # 计算Haversine距离误差
    haversine_errors = calculate_haversine_error(testY_inverse, predicted_inverse)
    avg_error = np.mean(haversine_errors)
    median_error = np.median(haversine_errors)
    
    print(f'平均Haversine误差: {avg_error:.2f} 米')
    print(f'中位数Haversine误差: {median_error:.2f} 米')
    
    # 计算不同阈值下的精度
    thresholds = [50, 80, 100, 150, 200]
    for threshold in thresholds:
        accuracy = np.sum(haversine_errors <= threshold) / len(haversine_errors) * 100
        print(f'预测精度（{threshold}米内）: {accuracy:.2f}%')
    
    # 9. 可视化结果
    print("\n=== 生成可视化图表 ===")
    plt.figure(figsize=(15, 10))
    
    # 训练历史
    plt.subplot(2, 3, 1)
    plt.plot(history.history['loss'], label='训练损失')
    plt.plot(history.history['val_loss'], label='验证损失')
    plt.title('模型训练历史')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    # 轨迹对比（取前500个点）
    plt.subplot(2, 3, 2)
    n_points = min(500, len(testY_inverse))
    plt.plot(testY_inverse[:n_points, 1], testY_inverse[:n_points, 0], 
             label='真实轨迹', marker='o', markersize=1, linestyle='-', alpha=0.7)
    plt.plot(predicted_inverse[:n_points, 1], predicted_inverse[:n_points, 0], 
             label='预测轨迹', marker='x', markersize=1, linestyle='--', alpha=0.7)
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.title('轨迹对比（前500点）')
    plt.legend()
    
    # 误差分布
    plt.subplot(2, 3, 3)
    plt.hist(haversine_errors, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('误差 (米)')
    plt.ylabel('频次')
    plt.title('预测误差分布')
    plt.axvline(avg_error, color='red', linestyle='--', label=f'平均误差: {avg_error:.1f}m')
    plt.legend()
    
    # 误差时间序列
    plt.subplot(2, 3, 4)
    plt.plot(haversine_errors[:1000])  # 只显示前1000个点
    plt.xlabel('样本索引')
    plt.ylabel('误差 (米)')
    plt.title('预测误差时间序列（前1000点）')
    
    # 精度对比
    plt.subplot(2, 3, 5)
    accuracies = []
    for threshold in thresholds:
        accuracy = np.sum(haversine_errors <= threshold) / len(haversine_errors) * 100
        accuracies.append(accuracy)
    plt.bar([str(t) for t in thresholds], accuracies)
    plt.xlabel('误差阈值 (米)')
    plt.ylabel('精度 (%)')
    plt.title('不同阈值下的预测精度')
    
    # 特征重要性可视化（显示输入特征的统计）
    plt.subplot(2, 3, 6)
    feature_names = ['纬度', '经度', '速度', '角度sin', '角度cos']
    feature_std = np.std(features, axis=0)
    plt.bar(feature_names, feature_std)
    plt.xlabel('特征')
    plt.ylabel('标准差')
    plt.title('输入特征变异性')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # 10. 保存模型
    print("\n=== 保存模型 ===")
    model.save('enhanced_lstm_model.keras')
    
    # 保存归一化器
    import joblib
    joblib.dump(scaler_position, 'scaler_position.pkl')
    joblib.dump(scaler_speed, 'scaler_speed.pkl')
    joblib.dump(scaler_angle, 'scaler_angle.pkl')
    
    print("✅ 增强LSTM模型训练完成！")
    print(f"模型已保存为: enhanced_lstm_model.keras")
    print(f"归一化器已保存为: scaler_*.pkl")
