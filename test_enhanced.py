import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from keras.models import load_model
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from haversine import haversine
import joblib
import os

plt.rcParams['font.sans-serif'] = ['SimHei']

def preprocess_features(data):
    """特征预处理 - 与训练时保持一致"""
    # 将角度转换为sin和cos分量
    data['angle_sin'] = np.sin(np.radians(data['angle']))
    data['angle_cos'] = np.cos(np.radians(data['angle']))
    
    # 选择特征：经度、纬度、速度、角度的sin和cos分量
    features = data[['lat', 'lon', 'speed', 'angle_sin', 'angle_cos']].values
    
    return features

def load_and_preprocess_trajectory(filepath):
    """加载并预处理单个轨迹文件"""
    try:
        # 判断文件类型
        if filepath.endswith('.plt'):
            # 原始PLT文件
            data = pd.read_csv(filepath, skiprows=6, header=None)
            data.columns = ['lat', 'lon', 'zero1', 'zero2', 'altitude', 'date', 'time']
            # 需要计算速度和角度，这里简化处理，设为0
            data['speed'] = 0.0
            data['angle'] = 0.0
            print("警告：PLT文件缺少速度和角度信息，建议使用处理后的CSV文件")
        else:
            # 处理后的CSV文件
            data = pd.read_csv(filepath)
        
        # 确保有必要的列
        required_cols = ['lat', 'lon', 'speed', 'angle']
        if not all(col in data.columns for col in required_cols):
            raise ValueError(f"文件缺少必要的列: {required_cols}")
        
        # 过滤异常值
        data = data[(data['speed'] >= 0) & (data['speed'] <= 50)]
        data = data[(data['angle'] >= 0) & (data['angle'] <= 360)]
        
        print(f"加载轨迹数据: {len(data)} 个点")
        return data
        
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def create_sequences(features_scaled, sequence_length=5):
    """创建输入序列"""
    X = []
    for i in range(len(features_scaled) - sequence_length):
        X.append(features_scaled[i:i+sequence_length])
    return np.array(X)

def calculate_haversine_error(y_true, y_pred):
    """计算Haversine距离误差"""
    errors = []
    for i in range(len(y_true)):
        true_point = (y_true[i][0], y_true[i][1])
        pred_point = (y_pred[i][0], y_pred[i][1])
        error = haversine(true_point, pred_point) * 1000  # 转换为米
        errors.append(error)
    return np.array(errors)

def main():
    # 1. 加载模型和归一化器
    print("=== 加载训练好的模型 ===")
    try:
        model = load_model('enhanced_lstm_model.keras')
        scaler_position = joblib.load('scaler_position.pkl')
        scaler_speed = joblib.load('scaler_speed.pkl')
        scaler_angle = joblib.load('scaler_angle.pkl')
        print("✅ 模型和归一化器加载成功")
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        print("请先运行 lstm_enhanced.py 训练模型")
        return
    
    # 2. 加载测试轨迹
    print("\n=== 加载测试轨迹 ===")
    # 可以选择不同的测试文件
    test_files = [
        'Processed/Data/001/Trajectory/20081024234405.csv',
        'Processed/Data/001/Trajectory/20081023055305.csv',
        'Processed/Data/001/Trajectory/20081023234104.csv'
    ]
    
    # 选择第一个可用的文件
    test_data = None
    test_file = None
    for file in test_files:
        if os.path.exists(file):
            test_data = load_and_preprocess_trajectory(file)
            if test_data is not None:
                test_file = file
                break
    
    if test_data is None:
        print("❌ 没有找到可用的测试文件")
        return
    
    print(f"使用测试文件: {test_file}")
    
    # 3. 特征预处理
    print("\n=== 特征预处理 ===")
    features = preprocess_features(test_data)
    print(f"特征维度: {features.shape}")
    
    # 4. 数据归一化（使用训练时的归一化器）
    print("\n=== 数据归一化 ===")
    features_scaled = np.zeros_like(features)
    features_scaled[:, :2] = scaler_position.transform(features[:, :2])    # lat, lon
    features_scaled[:, 2:3] = scaler_speed.transform(features[:, 2:3])     # speed
    features_scaled[:, 3:5] = scaler_angle.transform(features[:, 3:5])     # angle_sin, angle_cos
    
    # 5. 构建输入序列
    print("\n=== 构建预测序列 ===")
    sequence_length = 5
    X_test = create_sequences(features_scaled, sequence_length)
    print(f"输入序列形状: {X_test.shape}")
    
    if len(X_test) == 0:
        print("❌ 轨迹太短，无法构建预测序列")
        return
    
    # 6. 进行预测
    print("\n=== 进行轨迹预测 ===")
    predictions_scaled = model.predict(X_test, verbose=1)
    
    # 7. 反归一化
    predictions = scaler_position.inverse_transform(predictions_scaled)
    
    # 获取真实值（对应的目标点）
    true_trajectory = features[:, :2]  # 原始的lat, lon
    true_points = true_trajectory[sequence_length:]  # 对应预测的真实点
    
    # 8. 计算误差
    print("\n=== 误差分析 ===")
    haversine_errors = calculate_haversine_error(true_points, predictions)
    
    avg_error = np.mean(haversine_errors)
    median_error = np.median(haversine_errors)
    max_error = np.max(haversine_errors)
    min_error = np.min(haversine_errors)
    
    print(f"平均误差: {avg_error:.2f} 米")
    print(f"中位数误差: {median_error:.2f} 米")
    print(f"最大误差: {max_error:.2f} 米")
    print(f"最小误差: {min_error:.2f} 米")
    
    # 计算不同阈值下的精度
    thresholds = [50, 80, 100, 150, 200]
    print("\n精度统计:")
    for threshold in thresholds:
        accuracy = np.sum(haversine_errors <= threshold) / len(haversine_errors) * 100
        print(f"  {threshold}米内精度: {accuracy:.2f}%")
    
    # 9. 可视化结果
    print("\n=== 生成可视化结果 ===")
    plt.figure(figsize=(15, 10))
    
    # 完整轨迹对比
    plt.subplot(2, 3, 1)
    plt.plot(true_trajectory[:, 1], true_trajectory[:, 0], 
             label='完整真实轨迹', color='blue', linewidth=1, alpha=0.7)
    plt.plot(predictions[:, 1], predictions[:, 0], 
             label='预测轨迹', color='red', linestyle='--', linewidth=1, alpha=0.8)
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.title('完整轨迹对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 局部轨迹对比（前200个点）
    plt.subplot(2, 3, 2)
    n_points = min(200, len(predictions))
    plt.plot(true_points[:n_points, 1], true_points[:n_points, 0], 
             label='真实轨迹', marker='o', markersize=2, linestyle='-', alpha=0.7)
    plt.plot(predictions[:n_points, 1], predictions[:n_points, 0], 
             label='预测轨迹', marker='x', markersize=2, linestyle='--', alpha=0.8)
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.title(f'局部轨迹对比（前{n_points}点）')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 误差分布直方图
    plt.subplot(2, 3, 3)
    plt.hist(haversine_errors, bins=50, alpha=0.7, edgecolor='black')
    plt.axvline(avg_error, color='red', linestyle='--', label=f'平均: {avg_error:.1f}m')
    plt.axvline(median_error, color='green', linestyle='--', label=f'中位数: {median_error:.1f}m')
    plt.xlabel('误差 (米)')
    plt.ylabel('频次')
    plt.title('预测误差分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 误差时间序列
    plt.subplot(2, 3, 4)
    plt.plot(haversine_errors, linewidth=1)
    plt.axhline(avg_error, color='red', linestyle='--', alpha=0.7, label=f'平均误差')
    plt.xlabel('预测点索引')
    plt.ylabel('误差 (米)')
    plt.title('预测误差时间序列')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 精度条形图
    plt.subplot(2, 3, 5)
    accuracies = []
    for threshold in thresholds:
        accuracy = np.sum(haversine_errors <= threshold) / len(haversine_errors) * 100
        accuracies.append(accuracy)
    
    bars = plt.bar([str(t) for t in thresholds], accuracies, alpha=0.7)
    plt.xlabel('误差阈值 (米)')
    plt.ylabel('精度 (%)')
    plt.title('不同阈值下的预测精度')
    plt.grid(True, alpha=0.3)
    
    # 在条形图上添加数值标签
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{acc:.1f}%', ha='center', va='bottom')
    
    # 速度和角度信息
    plt.subplot(2, 3, 6)
    plt.plot(test_data['speed'][:len(haversine_errors)], alpha=0.7, label='速度 (m/s)')
    plt.plot(haversine_errors/10, alpha=0.7, label='误差/10 (米)')  # 缩放以便对比
    plt.xlabel('点索引')
    plt.ylabel('数值')
    plt.title('速度与预测误差关系')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 10. 保存结果
    print("\n=== 保存预测结果 ===")
    results_df = pd.DataFrame({
        'true_lat': true_points[:, 0],
        'true_lon': true_points[:, 1],
        'pred_lat': predictions[:, 0],
        'pred_lon': predictions[:, 1],
        'error_meters': haversine_errors,
        'speed': test_data['speed'][sequence_length:sequence_length+len(predictions)].values,
        'angle': test_data['angle'][sequence_length:sequence_length+len(predictions)].values
    })
    
    output_file = 'prediction_results.csv'
    results_df.to_csv(output_file, index=False)
    print(f"预测结果已保存到: {output_file}")
    
    print("\n✅ 增强模型测试完成！")

if __name__ == "__main__":
    main()
