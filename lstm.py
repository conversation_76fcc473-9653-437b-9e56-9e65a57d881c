import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Activation
from keras.optimizers import Adam

plt.rcParams['font.sans-serif'] = ['SimHei']

# 1. 读取 Geolife Trajectories 1.3 中的 .plt 文件
#data = pd.read_csv('Geolife Trajectories 1.3\\Data\\001\\Trajectory\\20081024234405.plt', skiprows=6, header=None)
#data.columns = ['lat', 'lon', 'zero', 'alt', 'date_days', 'date', 'time']
#data = data[['lat', 'lon']]

data = pd.read_csv('Processed\\Data\\001\\Trajectory\\20081024234405.csv', skiprows=1, header=None)
data.columns = ['lat','lon','speed','angle']
data = data[['lat', 'lon']]

print(f'样本数：{len(data)}, 维度：{data.shape[1]}')

# 2. 归一化
scaler = MinMaxScaler()
data_scaled = scaler.fit_transform(data)

# 3. 构造数据集
def create_dataset(dataset, time_step=5):
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        Y.append(dataset[i + time_step, :])
    return np.array(X), np.array(Y)

time_step = 5
X, Y = create_dataset(data_scaled, time_step)

# 4. 划分训练集和测试集
train_size = int(len(X) * 0.8)
trainX, trainY = X[:train_size], Y[:train_size]
testX, testY = X[train_size:], Y[train_size:]
print('Train X shape:', trainX.shape)
print('Train Y shape:', trainY.shape)
print('Test X shape:', testX.shape)
print('Test Y shape:', testY.shape)

# 5. LSTM v3 模型搭建
model = Sequential()
model.add(LSTM(units=120, return_sequences=True, input_shape=(time_step, 2)))
model.add(Dropout(0.2))
model.add(LSTM(units=120))
model.add(Dropout(0.2))
model.add(Dense(units=2))
model.add(Activation('linear'))

model.compile(loss='mse', optimizer=Adam(learning_rate=0.001), metrics=['mae'])
model.summary()

# 6. 模型训练
history = model.fit(trainX, trainY, epochs=100, batch_size=64, validation_split=0.2)

# 7. 模型评估
test_loss, test_mae = model.evaluate(testX, testY)
print(f'Test Loss: {test_loss:.4f}, Test MAE: {test_mae:.4f}')

# 8. 预测 & 反归一化
predicted = model.predict(testX)
predicted_inverse = scaler.inverse_transform(predicted)
testY_inverse = scaler.inverse_transform(testY)

# 9. 可视化
plt.figure(figsize=(10, 5))
plt.subplot(1, 2, 1)
plt.plot(testY_inverse[:, 1], testY_inverse[:, 0], label='真实轨迹', marker='o', markersize=2, linestyle='-')
plt.plot(predicted_inverse[:, 1], predicted_inverse[:, 0], label='预测轨迹', marker='x', markersize=2, linestyle='--')
plt.legend()
plt.title('真实轨迹 vs 预测轨迹')

plt.subplot(1, 2, 2)
error = np.sqrt(np.sum((testY_inverse - predicted_inverse) ** 2, axis=1))
plt.plot(error)
plt.title('轨迹点误差（欧氏距离）')
plt.tight_layout()
plt.show()

# 10. 保存模型
model.save('my_model1.keras')

