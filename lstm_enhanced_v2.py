import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Activation, BatchNormalization
from keras.optimizers import Adam
from keras.callbacks import EarlyStopping, ReduceLROnPlateau
from haversine import haversine
import os

plt.rcParams['font.sans-serif'] = ['SimHei']

def load_single_trajectory(filepath):
    """加载单个轨迹文件"""
    try:
        data = pd.read_csv(filepath)
        # 数据清洗
        data = data.dropna()
        data = data[(data['speed'] >= 0) & (data['speed'] <= 30)]  # 更严格的速度限制
        data = data[(data['angle'] >= 0) & (data['angle'] <= 360)]
        
        # 移除异常点（基于位置的突变）
        if len(data) > 1:
            lat_diff = np.abs(np.diff(data['lat']))
            lon_diff = np.abs(np.diff(data['lon']))
            # 移除位置变化过大的点（可能是GPS错误）
            outlier_mask = (lat_diff > 0.01) | (lon_diff > 0.01)
            outlier_indices = np.where(outlier_mask)[0] + 1
            data = data.drop(data.index[outlier_indices])
        
        return data.reset_index(drop=True)
    except Exception as e:
        print(f"加载文件 {filepath} 失败: {e}")
        return None

def enhanced_feature_engineering(data):
    """增强的特征工程"""
    # 基础特征
    features_df = data[['lat', 'lon', 'speed']].copy()
    
    # 角度特征 - 使用sin和cos避免周期性问题
    features_df['angle_sin'] = np.sin(np.radians(data['angle']))
    features_df['angle_cos'] = np.cos(np.radians(data['angle']))
    
    # 速度相关特征
    features_df['speed_log'] = np.log1p(data['speed'])  # 对数变换减少偏斜
    
    # 移动统计特征（窗口大小为3）
    window = 3
    features_df['speed_ma'] = data['speed'].rolling(window=window, center=True).mean().fillna(data['speed'])
    features_df['lat_ma'] = data['lat'].rolling(window=window, center=True).mean().fillna(data['lat'])
    features_df['lon_ma'] = data['lon'].rolling(window=window, center=True).mean().fillna(data['lon'])
    
    # 变化率特征
    features_df['lat_diff'] = data['lat'].diff().fillna(0)
    features_df['lon_diff'] = data['lon'].diff().fillna(0)
    features_df['speed_diff'] = data['speed'].diff().fillna(0)
    
    return features_df.values

def create_sequences_with_targets(features, sequence_length=5):
    """创建序列数据，输入包含所有特征，输出只预测位置"""
    X, Y = [], []
    for i in range(len(features) - sequence_length):
        # 输入：所有特征的序列
        X.append(features[i:i+sequence_length])
        # 输出：只预测下一个位置（lat, lon）
        Y.append(features[i + sequence_length, :2])  # 只取lat, lon
    return np.array(X), np.array(Y)

def build_improved_model(sequence_length, n_features):
    """构建改进的LSTM模型"""
    model = Sequential()
    
    # 第一层LSTM - 较小的单元数
    model.add(LSTM(units=64, return_sequences=True, input_shape=(sequence_length, n_features)))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))
    
    # 第二层LSTM
    model.add(LSTM(units=32, return_sequences=False))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))
    
    # 全连接层
    model.add(Dense(units=16, activation='relu'))
    model.add(Dropout(0.1))
    model.add(Dense(units=2))  # 输出lat, lon
    model.add(Activation('linear'))
    
    # 使用更小的学习率
    model.compile(loss='mse', optimizer=Adam(learning_rate=0.0005), metrics=['mae'])
    return model

def main():
    print("=== 改进版轨迹预测模型训练 ===")
    
    # 1. 加载数据 - 使用单个文件进行更好的控制
    filepath = 'Processed/Data/001/Trajectory/20081024234405.csv'
    if not os.path.exists(filepath):
        print(f"文件不存在: {filepath}")
        return
    
    data = load_single_trajectory(filepath)
    if data is None or len(data) < 100:
        print("数据加载失败或数据量不足")
        return
    
    print(f"加载数据: {len(data)} 个点")
    print("数据统计:")
    print(data[['lat', 'lon', 'speed', 'angle']].describe())
    
    # 2. 特征工程
    print("\n=== 特征工程 ===")
    features = enhanced_feature_engineering(data)
    print(f"特征维度: {features.shape}")
    
    # 3. 数据归一化 - 使用RobustScaler减少异常值影响
    print("\n=== 数据归一化 ===")
    scaler = RobustScaler()
    features_scaled = scaler.fit_transform(features)
    
    # 4. 创建序列数据
    print("\n=== 创建序列数据 ===")
    sequence_length = 5
    X, Y = create_sequences_with_targets(features_scaled, sequence_length)
    print(f"输入形状: {X.shape}, 输出形状: {Y.shape}")
    
    # 5. 划分数据集 - 使用时间序列的方式划分
    train_size = int(len(X) * 0.7)
    val_size = int(len(X) * 0.15)
    
    X_train = X[:train_size]
    Y_train = Y[:train_size]
    X_val = X[train_size:train_size+val_size]
    Y_val = Y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    Y_test = Y[train_size+val_size:]
    
    print(f"训练集: {X_train.shape}, 验证集: {X_val.shape}, 测试集: {X_test.shape}")
    
    # 6. 构建模型
    print("\n=== 构建模型 ===")
    model = build_improved_model(sequence_length, X.shape[2])
    model.summary()
    
    # 7. 设置回调函数
    callbacks = [
        EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True),
        ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=8, min_lr=1e-6)
    ]
    
    # 8. 训练模型
    print("\n=== 训练模型 ===")
    history = model.fit(
        X_train, Y_train,
        validation_data=(X_val, Y_val),
        epochs=100,
        batch_size=32,
        callbacks=callbacks,
        verbose=1
    )
    
    # 9. 评估模型
    print("\n=== 模型评估 ===")
    train_loss, train_mae = model.evaluate(X_train, Y_train, verbose=0)
    val_loss, val_mae = model.evaluate(X_val, Y_val, verbose=0)
    test_loss, test_mae = model.evaluate(X_test, Y_test, verbose=0)
    
    print(f"训练集 - Loss: {train_loss:.6f}, MAE: {train_mae:.6f}")
    print(f"验证集 - Loss: {val_loss:.6f}, MAE: {val_mae:.6f}")
    print(f"测试集 - Loss: {test_loss:.6f}, MAE: {test_mae:.6f}")
    
    # 10. 预测和误差分析
    print("\n=== 预测分析 ===")
    Y_pred = model.predict(X_test, verbose=0)
    
    # 反归一化 - 只对位置特征反归一化
    # 创建临时数组用于反归一化
    temp_true = np.zeros((len(Y_test), features.shape[1]))
    temp_pred = np.zeros((len(Y_pred), features.shape[1]))
    temp_true[:, :2] = Y_test
    temp_pred[:, :2] = Y_pred
    
    # 反归一化
    Y_test_inverse = scaler.inverse_transform(temp_true)[:, :2]
    Y_pred_inverse = scaler.inverse_transform(temp_pred)[:, :2]
    
    # 计算Haversine距离误差
    errors = []
    for i in range(len(Y_test_inverse)):
        true_point = (Y_test_inverse[i][0], Y_test_inverse[i][1])
        pred_point = (Y_pred_inverse[i][0], Y_pred_inverse[i][1])
        error = haversine(true_point, pred_point) * 1000  # 转换为米
        errors.append(error)
    
    errors = np.array(errors)
    
    print(f"平均误差: {np.mean(errors):.2f} 米")
    print(f"中位数误差: {np.median(errors):.2f} 米")
    print(f"标准差: {np.std(errors):.2f} 米")
    
    # 精度统计
    thresholds = [50, 80, 100, 150, 200]
    for threshold in thresholds:
        accuracy = np.sum(errors <= threshold) / len(errors) * 100
        print(f"{threshold}米内精度: {accuracy:.2f}%")
    
    # 11. 可视化
    print("\n=== 生成可视化 ===")
    plt.figure(figsize=(15, 10))
    
    # 训练历史
    plt.subplot(2, 3, 1)
    plt.plot(history.history['loss'], label='训练损失')
    plt.plot(history.history['val_loss'], label='验证损失')
    plt.title('训练历史')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # 轨迹对比
    plt.subplot(2, 3, 2)
    n_points = min(200, len(Y_test_inverse))
    plt.plot(Y_test_inverse[:n_points, 1], Y_test_inverse[:n_points, 0], 
             'o-', label='真实轨迹', markersize=2, alpha=0.7)
    plt.plot(Y_pred_inverse[:n_points, 1], Y_pred_inverse[:n_points, 0], 
             'x--', label='预测轨迹', markersize=2, alpha=0.7)
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.title(f'轨迹对比（前{n_points}点）')
    plt.legend()
    plt.grid(True)
    
    # 误差分布
    plt.subplot(2, 3, 3)
    plt.hist(errors, bins=30, alpha=0.7, edgecolor='black')
    plt.axvline(np.mean(errors), color='red', linestyle='--', 
                label=f'平均: {np.mean(errors):.1f}m')
    plt.xlabel('误差 (米)')
    plt.ylabel('频次')
    plt.title('误差分布')
    plt.legend()
    plt.grid(True)
    
    # 误差时间序列
    plt.subplot(2, 3, 4)
    plt.plot(errors)
    plt.axhline(np.mean(errors), color='red', linestyle='--', alpha=0.7)
    plt.xlabel('预测点索引')
    plt.ylabel('误差 (米)')
    plt.title('误差时间序列')
    plt.grid(True)
    
    # 精度条形图
    plt.subplot(2, 3, 5)
    accuracies = [np.sum(errors <= t) / len(errors) * 100 for t in thresholds]
    plt.bar([str(t) for t in thresholds], accuracies, alpha=0.7)
    plt.xlabel('误差阈值 (米)')
    plt.ylabel('精度 (%)')
    plt.title('不同阈值下的精度')
    plt.grid(True)
    
    # 学习率变化
    plt.subplot(2, 3, 6)
    if 'lr' in history.history:
        plt.plot(history.history['lr'])
        plt.title('学习率变化')
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.yscale('log')
        plt.grid(True)
    else:
        plt.text(0.5, 0.5, '学习率历史不可用', ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('学习率变化')
    
    plt.tight_layout()
    plt.show()
    
    # 12. 保存模型
    print("\n=== 保存模型 ===")
    model.save('enhanced_lstm_v2.keras')
    
    # 保存归一化器
    import joblib
    joblib.dump(scaler, 'scaler_v2.pkl')
    
    # 保存预测结果
    results_df = pd.DataFrame({
        'true_lat': Y_test_inverse[:, 0],
        'true_lon': Y_test_inverse[:, 1],
        'pred_lat': Y_pred_inverse[:, 0],
        'pred_lon': Y_pred_inverse[:, 1],
        'error_meters': errors
    })
    results_df.to_csv('enhanced_v2_results.csv', index=False)
    
    print("✅ 改进版模型训练完成！")
    print(f"模型保存为: enhanced_lstm_v2.keras")
    print(f"归一化器保存为: scaler_v2.pkl")
    print(f"结果保存为: enhanced_v2_results.csv")

if __name__ == "__main__":
    main()
